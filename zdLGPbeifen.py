"""
GaPP kernel for reconstruction z-dL relation from Supernovae Ia data.
"""
# ======== important packages to be imported 
def warn(*args, **kwargs):
    pass
import warnings
warnings.warn = warn

#set the python path including gapp and the subpackage covfunctions in GaPP3 
import sys
sys.path.insert(0,'/Users/<USER>/Downloads/GaPP/') # please set your location where you put GaPP3 directory
sys.path.insert(0,'/Users/<USER>/Downloads/GaPP/gapp/')
sys.path.insert(0,'/Users/<USER>/Downloads/GaPP/gapp/covfunctions/')
print(sys.path)

from gapp import gp, dgp, mcmcgp, mcmcdgp, covariance
import numpy as np
from numpy import loadtxt, savetxt, array, concatenate, ones, random, reshape, shape, zeros, linspace, sqrt
import matplotlib.pyplot as plt
import emcee
from astropy.cosmology import FlatLambdaCDM
import astropy.units as u

#======== loading cosmological data
filename = 'data/zdL_SNIa.txt' # 定义文件名变量
(z, dL, dL_err) = loadtxt(filename, unpack=True, skiprows=1)  # 使用变量加载数据

#======== Gaussian process: reconstructing dL(z) from data, starting from zmin = 0 all the way to zmax from data 
zmin = 0.
zmax = np.max(z)
g1 = gp.GaussianProcess(z, dL, dL_err,covfunction=covariance.SquaredExponential,cXstar=(zmin,zmax,1000))
#g2 = gp.GaussianProcess(Z,Hz,Sigma,covfunction=covariance.DoubleSquaredExponential,cXstar=(zmin,zmax,200))
#g3 = gp.GaussianProcess(Z,Hz,Sigma,covfunction=covariance.Matern92,cXstar=(zmin,zmax,200))
#g4 = gp.GaussianProcess(Z,Hz,Sigma,covfunction=covariance.Matern72,cXstar=(zmin,zmax,200))
#g5 = gp.GaussianProcess(Z,Hz,Sigma,covfunction=covariance.Matern52,cXstar=(zmin,zmax,200))
#g6 = gp.GaussianProcess(Z,Hz,Sigma,covfunction=covariance.Matern32,cXstar=(zmin,zmax,200))

# ======== training of the hyperparameters and reconstruction of the function
(rec1,theta1) = g1.gp(thetatrain=True)
   

#========  creating variables to receive the GP reconstructed dL(z) 
zrec = rec1[:,0]
dLrec = rec1[:,1]
sigdLrec = rec1[:,2] 

# ======== Define Theoretical Cosmological Models
# Planck-ΛCDM (arXiv:1807.06209)
cosmo_planck = FlatLambdaCDM(H0=67.4, Om0=0.315)
# SH0ES-ΛCDM (arXiv:2112.04510)
cosmo_shoes = FlatLambdaCDM(H0=73.04, Om0=0.315)

# ======== Calculate theoretical luminosity distances
# 使用与重建相同的zrec点
dL_planck_theory = cosmo_planck.luminosity_distance(zrec).to(u.Mpc).value
dL_shoes_theory = cosmo_shoes.luminosity_distance(zrec).to(u.Mpc).value

# ========== saving the reconstructed dL(z) and its uncertainty
savetxt("dL_rec.txt",rec1)
  
####################################################################################################
# ======= OPTIONAL: plotting the reconstructed dL(z) curve
#latex rendering text fonts
plt.rc('text', usetex=True)
plt.rc('font', family='serif')

# ====== Create figure size in inches
plt.figure(figsize=(9., 7.))

# ========= Define axes
plt.xlabel(r"$z$", fontsize=22)
plt.ylabel(r"$d_L(z)$ (Mpc)", fontsize=22)
plt.xlim(zmin, 2.4)
plt.xticks(fontsize=22)
plt.yticks(fontsize=22)
    
# ========== Plotting the real data points and reconstructed dL(z) curves - from 1 to 3sigma
plt.errorbar(z, dL, yerr=dL_err, fmt='o', color='black', label=r"Supernovae data")
plt.fill_between(zrec, dLrec+1.*sigdLrec, dLrec-1.*sigdLrec, facecolor='#F08080', alpha=0.80, interpolate=True, label=r"$1\sigma$")
plt.fill_between(zrec, dLrec+2.*sigdLrec, dLrec-2.*sigdLrec, facecolor='#F08080', alpha=0.50, interpolate=True, label=r"$2\sigma$")
plt.fill_between(zrec, dLrec+3.*sigdLrec, dLrec-3.*sigdLrec, facecolor='#F08080', alpha=0.30, interpolate=True, label=r"$3\sigma$")

# ========== Plotting the mean reconstructed dL(z) curve
plt.plot(zrec, dLrec, color='darkred', linestyle='-', linewidth=2, label=r"Reconstructed $z-d_L(z)$")

# ========== Plotting theoretical dL(z) curves
plt.plot(zrec, dL_planck_theory, color='blue', linestyle='--', label=r'$Planck-\Lambda CDM$')
plt.plot(zrec, dL_shoes_theory, color='green', linestyle='-.', label=r'$SH0ES-\Lambda CDM$')

# # Add legends (in the specified order)
handles, labels = plt.gca().get_legend_handles_labels()
order = [
    labels.index("Supernovae data"),
    labels.index(r'$Planck-\Lambda CDM$'),
    labels.index(r'$SH0ES-\Lambda CDM$'),
    labels.index(r"Reconstructed $z-d_L(z)$"),
    labels.index(r"$1\sigma$"),
    labels.index(r"$2\sigma$"),
    labels.index(r"$3\sigma$")
]
plt.legend([handles[i] for i in order], [labels[i] for i in order],
           loc='upper left', frameon=True, edgecolor='black', facecolor='white',
           framealpha=1, fontsize=12, ncol=1)

# =========== saving the plot
plt.savefig(filename+'_reconst.png')

plt.show()


