# GaPP3
The updated version of GaPP (Gaussian Process in Python) with Python3

For the original version and the manual materials of GaPP please refere to <PERSON>, <PERSON>, <PERSON><PERSON>, Reconstruction of dark energy and expansion dynamics using Gaussian processes, arXiv:1204.2832.


To use GaPP3,  first use miniconda to build an envirenment with python=3.X, numpy, scipy, matplotlib, emcee, corner (if you do not use MCMC to optimize the hyperparameters of covaraince function, then numpy, scipy, and matplotlib are enough). In the python script we should include the GaPP3 directory into PYTHONPATH.


---------------------------------------------------------------------
# ======== important packages to be imported 
def warn(*args, **kwargs):
    pass
import warnings
warnings.warn = warn

#set the python path including gapp and the subpackage covfunctions in GaPP3 
import sys, os
script_dir = os.path.dirname(os.path.abspath(__file__))
gapp_base_dir = os.path.abspath(os.path.join(script_dir, '..', '..', '..', 'GaPP'))

sys.path.insert(0, gapp_base_dir) 
sys.path.insert(0, os.path.join(gapp_base_dir, 'gapp'))
sys.path.insert(0, os.path.join(gapp_base_dir, 'gapp', 'covfunctions'))
print(sys.path)

-----------------------------------------------------------------------

Please replace the location with your own location of GaPP3.

For the basic examples please refere to the examples directory.

For the manual book please see the original documentation of gapp. The usage is not changed. Note now we do not need acor package for MCMC. 

